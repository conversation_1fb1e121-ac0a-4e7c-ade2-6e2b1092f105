import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { gradeSubmission } from '../utils/api';
import { CheckCircleIcon, XCircleIcon, FileIcon, DownloadIcon } from 'lucide-react';
import { getSubmissionById, updateSubmission, createNotification } from '../utils/supabase';
import BackButton from '../components/BackButton';
import LoadingSpinner from '../components/LoadingSpinner';
import ErrorAlert from '../components/ErrorAlert';
import { toast } from 'react-toastify';

interface GradingResult {
  final_marks: number;
  max_marks: number;
  percentage: number;
  grade_letter: string;
  review: string;
  improvement?: number;
  breakdown?: Record<string, string>;
  graded_at: string;
  grading_mode: string;
}

const ReviewSubmission: React.FC = () => {
  const { assignmentId, submissionId } = useParams<{ assignmentId: string; submissionId: string }>();
  const navigate = useNavigate();

  // States
  const [submission, setSubmission] = useState<any>(null);
  const [gradingMode, setGradingMode] = useState<string>("Compare OCR'd content with Generated document");
  const [gradingCriteria, setGradingCriteria] = useState<string>('');
  const [customInstructions, setCustomInstructions] = useState<string>('');
  const [isGrading, setIsGrading] = useState<boolean>(false);
  const [gradingResult, setGradingResult] = useState<GradingResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isEditingGrade, setIsEditingGrade] = useState<boolean>(false);
  const [manualGrade, setManualGrade] = useState<number>(0);
  const [manualFeedback, setManualFeedback] = useState<string>('');
  
  // Fetch submission and assignment data
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        if (!submissionId) {
          throw new Error('Submission ID is required');
        }

        const { data: submissionData, error: submissionError } = await getSubmissionById(submissionId);

        if (submissionError) {
          throw submissionError;
        }

        if (!submissionData) {
          throw new Error('Submission not found');
        }

        setSubmission(submissionData);

        // Set existing grade as grading result if available
        if (submissionData.grade !== null) {
          setGradingResult({
            final_marks: submissionData.grade,
            max_marks: submissionData.assignments.max_marks,
            percentage: Math.round((submissionData.grade / submissionData.assignments.max_marks) * 100),
            grade_letter: getGradeLetter((submissionData.grade / submissionData.assignments.max_marks) * 100),
            review: submissionData.feedback || 'No feedback provided',
            graded_at: submissionData.graded_at || new Date().toISOString(),
            grading_mode: "Compare OCR'd content with Generated document"
          });
          setManualGrade(submissionData.grade);
          setManualFeedback(submissionData.feedback || '');
        } else {
          setManualGrade(0);
          setManualFeedback('');
        }

      } catch (error: any) {
        console.error('Error fetching data:', error);
        setError(error.message || 'Failed to load submission data');
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchData();
  }, [assignmentId, submissionId]);

  const getGradeLetter = (percentage: number): string => {
    if (percentage >= 90) return 'A';
    if (percentage >= 80) return 'B';
    if (percentage >= 70) return 'C';
    if (percentage >= 60) return 'D';
    return 'F';
  };

  const handleManualGrade = async () => {
    if (manualGrade < 0 || manualGrade > submission.assignments.max_marks) {
      setError(`Grade must be between 0 and ${submission.assignments.max_marks}`);
      return;
    }

    setIsGrading(true);
    setError(null);

    try {
      const percentage = Math.round((manualGrade / submission.assignments.max_marks) * 100);

      // Update submission with manual grade
      await updateSubmission(submission.id, {
        grade: manualGrade,
        feedback: manualFeedback,
        graded_at: new Date().toISOString(),
        graded_by: 'manual'
      });

      // Update grading result display
      setGradingResult({
        final_marks: manualGrade,
        max_marks: submission.assignments.max_marks,
        percentage: percentage,
        grade_letter: getGradeLetter(percentage),
        review: manualFeedback,
        graded_at: new Date().toISOString(),
        grading_mode: 'Manual grading by teacher'
      });

      // Create notification for student about grade update
      await createNotification({
        user_id: submission.users.id,
        title: 'Grade Updated',
        message: `Your grade for "${submission.assignments.title}" has been updated to ${manualGrade}/${submission.assignments.max_marks}`,
        type: 'grade',
        related_id: submission.id
      });

      setIsEditingGrade(false);
      toast.success('Grade updated successfully!');
    } catch (err: any) {
      setError(err.message || 'Failed to update grade');
      console.error('Error updating grade:', err);
    } finally {
      setIsGrading(false);
    }
  };
  
  // Handle grading
  const handleGrade = async () => {
    setIsGrading(true);
    setError(null);

    try {
      const generatedContent = {
        content: submission.assignments.content,
        max_marks: submission.assignments.max_marks,
        marks: Math.floor(submission.assignments.max_marks * 0.8),
        due_date: submission.assignments.due_date,
        generated_at: submission.assignments.created_at,
        prompt_used: submission.assignments.ai_prompt || 'Standard assignment'
      };

      const response = await gradeSubmission(
        gradingMode,
        submission.ocr_text,
        generatedContent,
        gradingCriteria || undefined,
        customInstructions || undefined
      );

      if (response.success) {
        setGradingResult(response.data);

        // Update submission with new grade
        await updateSubmission(submission.id, {
          grade: response.data.final_marks,
          feedback: response.data.review,
          graded_at: new Date().toISOString(),
          graded_by: 'manual' // Manual grading by teacher
        });

        toast.success('Submission graded successfully!');
      } else {
        setError(response.error || 'Grading failed');
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred during grading');
      console.error('Grading error:', err);
    } finally {
      setIsGrading(false);
    }
  };
  
  if (error) {
    return (
      <div className="p-6">
        <BackButton />
        <ErrorAlert 
          message={error} 
          onDismiss={() => setError(null)} 
          className="mt-4"
        />
      </div>
    );
  }
  
  if (isLoading) {
    return (
      <div className="p-6">
        <BackButton />
        <div className="h-64 flex items-center justify-center">
          <LoadingSpinner size="medium" />
        </div>
      </div>
    );
  }

  if (!submission) {
    return (
      <div className="p-6">
        <BackButton />
        <div className="text-center">
          <p className="text-gray-500">Submission not found</p>
        </div>
      </div>
    );
  }
  
  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="mb-6">
        <BackButton />
      </div>
      
      <div className="bg-white border border-gray-200 rounded-lg overflow-hidden mb-6">
        <div className="p-6">
          <h1 className="text-2xl font-bold mb-2">Review Submission</h1>
          <div className="text-gray-600 mb-4">
            Assignment: {submission.assignments.title} • Student: {submission.users.name}
          </div>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">Assignment</h2>
          <h3 className="text-lg font-medium mb-2">{submission.assignments.title}</h3>
          <div className="mb-4">
            <span className="text-sm font-medium text-gray-500">Max Marks:</span>
            <span className="ml-2">{submission.assignments.max_marks}</span>
          </div>
          <div className="mb-4">
            <span className="text-sm font-medium text-gray-500">Due Date:</span>
            <span className="ml-2">{new Date(submission.assignments.due_date).toLocaleDateString()}</span>
          </div>
          <div className="p-4 bg-gray-50 rounded-md">
            <h4 className="text-sm font-medium text-gray-500 mb-2">Content:</h4>
            <div className="whitespace-pre-wrap">{submission.assignments.content}</div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">Student Submission</h2>
          <div className="mb-4">
            <span className="text-sm font-medium text-gray-500">Student:</span>
            <span className="ml-2">{submission.users.name}</span>
          </div>
          <div className="mb-4">
            <span className="text-sm font-medium text-gray-500">Email:</span>
            <span className="ml-2">{submission.users.email}</span>
          </div>
          <div className="mb-4">
            <span className="text-sm font-medium text-gray-500">Submitted:</span>
            <span className="ml-2">{new Date(submission.submitted_at).toLocaleString()}</span>
          </div>
          <div className="mb-4">
            <span className="text-sm font-medium text-gray-500">File:</span>
            <div className="ml-2 flex items-center">
              <FileIcon className="h-4 w-4 mr-1" />
              <span className="mr-2">{submission.file_name}</span>
              <a
                href={submission.file_url}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:text-blue-800 flex items-center"
              >
                <DownloadIcon className="h-4 w-4 mr-1" />
                View File
              </a>
            </div>
          </div>
          <div className="mb-4">
            <span className="text-sm font-medium text-gray-500">Status:</span>
            <span className="ml-2 px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800 items-center">
              <CheckCircleIcon className="h-4 w-4 mr-1" />
              Submitted
            </span>
          </div>
          <div className="p-4 bg-gray-50 rounded-md">
            <h4 className="text-sm font-medium text-gray-500 mb-2">Extracted Text (OCR):</h4>
            <div className="whitespace-pre-wrap max-h-64 overflow-y-auto">{submission.ocr_text}</div>
          </div>
        </div>
      </div>
      
      <div className="mt-8 bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-semibold mb-4">Grading Options</h2>
        
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">Grading Mode</label>
          <select
            value={gradingMode}
            onChange={(e) => setGradingMode(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
            disabled={isGrading}
          >
            <option value="Compare OCR'd content with Generated document">Compare with Original</option>
            <option value="Grade OCR'd content only">Grade Submission Only</option>
            <option value="Grade Generated content only">Evaluate Assignment Quality</option>
          </select>
        </div>
        
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">Grading Criteria (Optional)</label>
          <textarea
            value={gradingCriteria}
            onChange={(e) => setGradingCriteria(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
            rows={3}
            placeholder="E.g., Focus on mathematical accuracy and step-by-step solutions"
            disabled={isGrading}
          />
        </div>
        
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">Custom Instructions (Optional)</label>
          <textarea
            value={customInstructions}
            onChange={(e) => setCustomInstructions(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
            rows={3}
            placeholder="E.g., Be lenient on formatting but strict on conceptual understanding"
            disabled={isGrading}
          />
        </div>
        
        <div className="flex space-x-4">
          <button
            onClick={handleGrade}
            disabled={isGrading}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-blue-300 disabled:cursor-not-allowed"
          >
            {isGrading ? (
              <span className="flex items-center">
                <span className="inline-block animate-spin mr-2">⟳</span>
                Grading...
              </span>
            ) : 'Auto Grade'}
          </button>

          <button
            onClick={() => setIsEditingGrade(!isEditingGrade)}
            className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
          >
            {isEditingGrade ? 'Cancel Manual Grade' : 'Manual Grade'}
          </button>
        </div>
      </div>

      {/* Manual Grading Section */}
      {isEditingGrade && (
        <div className="mt-6 bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">Manual Grading</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Grade (out of {submission.assignments.max_marks})
              </label>
              <input
                type="number"
                min="0"
                max={submission.assignments.max_marks}
                value={manualGrade}
                onChange={(e) => setManualGrade(Number(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                disabled={isGrading}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Percentage
              </label>
              <input
                type="text"
                value={submission.assignments.max_marks > 0 ? `${Math.round((manualGrade / submission.assignments.max_marks) * 100)}%` : '0%'}
                readOnly
                className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50"
              />
            </div>
          </div>

          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Feedback
            </label>
            <textarea
              value={manualFeedback}
              onChange={(e) => setManualFeedback(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
              rows={4}
              placeholder="Provide feedback for the student..."
              disabled={isGrading}
            />
          </div>

          <button
            onClick={handleManualGrade}
            disabled={isGrading}
            className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:bg-green-300 disabled:cursor-not-allowed"
          >
            {isGrading ? 'Saving...' : 'Save Grade'}
          </button>
        </div>
      )}
      
      {gradingResult && (
        <div className="mt-8 bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">Grading Results</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="bg-gray-50 p-4 rounded-md">
              <h3 className="text-sm font-medium text-gray-500 mb-1">Marks</h3>
              <p className="text-2xl font-bold">{gradingResult.final_marks} / {gradingResult.max_marks}</p>
              <p className="text-sm text-gray-500">{gradingResult.percentage.toFixed(1)}%</p>
            </div>
            
            <div className="bg-gray-50 p-4 rounded-md">
              <h3 className="text-sm font-medium text-gray-500 mb-1">Grade</h3>
              <p className="text-2xl font-bold">{gradingResult.grade_letter}</p>
              {gradingResult.improvement !== undefined && (
                <p className="text-sm text-gray-500">
                  {gradingResult.improvement > 0 ? (
                    <span className="text-green-600">+{gradingResult.improvement} improvement</span>
                  ) : gradingResult.improvement < 0 ? (
                    <span className="text-red-600">{gradingResult.improvement} decrease</span>
                  ) : (
                    <span>No change</span>
                  )}
                </p>
              )}
            </div>
            
            <div className="bg-gray-50 p-4 rounded-md">
              <h3 className="text-sm font-medium text-gray-500 mb-1">Graded At</h3>
              <p className="text-lg font-medium">{new Date(gradingResult.graded_at).toLocaleString()}</p>
              <p className="text-sm text-gray-500">{gradingResult.grading_mode}</p>
            </div>
          </div>
          
          <div className="mb-6">
            <h3 className="text-lg font-medium mb-2">Review</h3>
            <div className="p-4 bg-gray-50 rounded-md whitespace-pre-wrap">
              {gradingResult.review}
            </div>
          </div>
          
          {gradingResult.breakdown && (
            <div>
              <h3 className="text-lg font-medium mb-2">Breakdown</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {Object.entries(gradingResult.breakdown).map(([category, score]) => (
                  <div key={category} className="bg-gray-50 p-4 rounded-md">
                    <h4 className="text-sm font-medium text-gray-500 mb-1">{category}</h4>
                    <p className="text-lg font-medium">{score}</p>
                  </div>
                ))}
              </div>
            </div>
          )}
          
          <div className="mt-6 flex justify-end">
            <button
              onClick={() => navigate(`/assignments/${assignmentId}`)}
              className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
            >
              Back to Assignment
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ReviewSubmission;