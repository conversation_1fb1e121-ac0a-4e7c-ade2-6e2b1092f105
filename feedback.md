Working Features (donot modify them ever unless explicitly told): 
1. Sign in and sign up
2. Joining class with a code
3. AI assignment generation.
4. Profile updating, uploading image works

Errors:
1. Tried creating assignment but got this:
Error creating assignment: {code: 'PGRST204', details: null, hint: null, message: "Could not find the 'description' column of 'assignments' in the schema cache"}code: "PGRST204"details: nullhint: nullmessage: "Could not find the 'description' column of 'assignments' in the schema cache"[[Prototype]]: Object
If i cant create an assignmnet, i wont be able to test assignment submission and grading. 

2. If i join the class as a student, it is not showing me the teacher in peoples section.

3. Overall people section on the left is not displaying any information. Users should be able to select a class and see its people there.

4. The claendar must always be there whether the assignment dealines are there or not. BUt when there is a deadline, it must show it.

5. The site is breaking down when im going to peoples section inside a class.

6. Please if the routing needs fixing then do so. I can see so many numbers and stuff i dont think this is how websites route. 

7. Remove unnecessary or unused files from the project. Clean it up. DONOT MESS IT UP BE VERY CAREFUL.

Not tested (check for their implementation so i can test later when fixed):
1. Student management.
2. Grades dispute
3. Assignment submission. 
4. Grading process
5. OCR process
6. Notification when a new assignment is there, new student comes etc.

