import axios from 'axios';
// const API_BASE_URL = 'http://localhost:5001';
const API_BASE_URL = 'https://fyp-backend-xah8.onrender.com';
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json'
  }
});
// Health check
export const checkHealth = async () => {
  const response = await api.get('/health');
  return response.data;
};
// OCR text extraction
export const extractText = async (file: File) => {
  console.log('Calling extractText API with file:', file.name, file.type, file.size);
  const formData = new FormData();
  formData.append('file', file);
  try {
    const response = await api.post('/api/ocr/extract', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    console.log('extractText API response:', response.status, response.data);
    return response.data;
  } catch (error: any) {
    console.error('extractText API error:', error.response?.status, error.response?.data || error.message);
    throw error;
  }
};
// AI document generation
export const generateDocument = async (prompt: string, maxMarks: number = 100, daysUntilDue: number = 7) => {
  console.log('Calling generateDocument API with:', { prompt, maxMarks, daysUntilDue });
  try {
    const response = await api.post('/api/generate/document', {
      prompt,
      max_marks: maxMarks,
      days_until_due: daysUntilDue
    });
    console.log('generateDocument API response:', response.status, response.data);
    return response.data;
  } catch (error: any) {
    console.error('generateDocument API error:', error.response?.status, error.response?.data || error.message);
    throw error;
  }
};
// Submission grading
export const gradeSubmission = async (gradingMode: string, ocrText: string | null, generatedContent: any | null, gradingCriteria?: string, customInstructions?: string) => {
  console.log('Calling gradeSubmission API with:', { gradingMode, ocrTextLength: ocrText?.length, generatedContent, gradingCriteria, customInstructions });
  try {
    const response = await api.post('/api/grade/submission', {
      grading_mode: gradingMode,
      ocr_text: ocrText,
      generated_content: generatedContent,
      grading_criteria: gradingCriteria,
      custom_instructions: customInstructions
    });
    console.log('gradeSubmission API response:', response.status, response.data);
    return response.data;
  } catch (error: any) {
    console.error('gradeSubmission API error:', error.response?.status, error.response?.data || error.message);
    throw error;
  }
};
// Mock API for classes, assignments, and grades
// In a real app, these would be actual API calls to your backend
export const getClasses = async () => {
  console.log('Calling getClasses API');
  try {
    const response = await api.get('/api/classes');
    console.log('getClasses API response:', response.status, response.data);
    return response.data;
  } catch (error: any) {
    console.error('Error fetching classes:', error.response?.status, error.response?.data || error.message);
    throw error;
  }
};

export const createClass = async (classData: {
  name: string;
  subject: string;
  description: string;
  color_scheme: string;
}) => {
  console.log('Calling createClass API with:', classData);
  try {
    const response = await api.post('/api/classes', classData);
    console.log('createClass API response:', response.status, response.data);
    return response.data;
  } catch (error: any) {
    console.error('createClass API error:', error.response?.status, error.response?.data || error.message);
    throw error;
  }
};
export const getAssignments = async (classId: string) => {
  console.log('Calling getAssignments API with classId:', classId);
  // Mock data - in a real app, this would be an API call
  try {
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 300));
    const assignments = [{
      id: '1',
      title: 'Quadratic Equations',
      dueDate: '2024-01-15T23:59:59.000Z',
      maxMarks: 100,
      classId: '1'
    }, {
      id: '2',
      title: "Newton's Laws",
      dueDate: '2024-01-16T23:59:59.000Z',
      maxMarks: 100,
      classId: '2'
    }, {
      id: '3',
      title: 'Chemical Bonding',
      dueDate: '2024-01-18T23:59:59.000Z',
      maxMarks: 100,
      classId: '3'
    }].filter(a => a.classId === classId || classId === 'all');
    console.log('getAssignments API response:', assignments);
    return assignments;
  } catch (error) {
    console.error('getAssignments API error:', error);
    throw error;
  }
};
export const getGrades = async (classId: string) => {
  // Mock data
  return [{
    id: '1',
    studentName: 'Alice Johnson',
    assignment: 'Quadratic Equations',
    marks: 85,
    maxMarks: 100,
    submitted: true
  }, {
    id: '2',
    studentName: 'Bob Smith',
    assignment: 'Quadratic Equations',
    marks: 92,
    maxMarks: 100,
    submitted: true
  }, {
    id: '3',
    studentName: 'Charlie Brown',
    assignment: 'Quadratic Equations',
    marks: 78,
    maxMarks: 100,
    submitted: true
  }, {
    id: '4',
    studentName: 'Diana Prince',
    assignment: 'Quadratic Equations',
    marks: null,
    maxMarks: 100,
    submitted: false
  }];
};
export const updateGrade = async (gradeId: string, marks: number) => {
  console.log('Calling updateGrade API with:', { gradeId, marks });
  // Mock update - would be a real API call in production
  try {
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 300));
    const result = {
      success: true,
      gradeId,
      marks
    };
    console.log('updateGrade API response:', result);
    return result;
  } catch (error) {
    console.error('updateGrade API error:', error);
    throw error;
  }
};

// Create assignment
export const createAssignment = async (classId: string, assignmentData: any) => {
  console.log('Calling createAssignment API with:', { classId, assignmentData });
  // Mock create - would be a real API call in production
  try {
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    const newAssignment = {
      id: Math.floor(Math.random() * 10000).toString(),
      classId,
      ...assignmentData,
      dueDate: new Date(Date.now() + assignmentData.daysUntilDue * 86400000).toISOString()
    };
    console.log('createAssignment API response:', newAssignment);
    return {
      success: true,
      assignment: newAssignment
    };
  } catch (error) {
    console.error('createAssignment API error:', error);
    throw error;
  }
};