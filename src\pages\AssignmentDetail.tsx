import React, { useEffect, useState } from 'react';
import { use<PERSON>ara<PERSON>, Link, useNavigate } from 'react-router-dom';
import { CalendarIcon, FileTextIcon, CheckCircleIcon, XCircleIcon } from 'lucide-react';
import { useAuth } from '../context/AuthContext';
import BackButton from '../components/BackButton';
import LoadingSpinner from '../components/LoadingSpinner';
import ErrorAlert from '../components/ErrorAlert';
import { getAssignmentDetails, getAssignmentSubmissions, getSubmission, getTicketsBySubmission } from '../utils/supabase';
import CreateTicket from '../components/CreateTicket';
const AssignmentDetail = () => {
  const {
    classId,
    assignmentId
  } = useParams();
  const {
    user,
    isTeacherForClass
  } = useAuth();
  const [assignment, setAssignment] = useState<any>(null);
  const [submissions, setSubmissions] = useState([]);
  const [userSubmission, setUserSubmission] = useState<any>(null);
  const [userTickets, setUserTickets] = useState<any[]>([]);
  const [showCreateTicket, setShowCreateTicket] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isTeacher, setIsTeacher] = useState(false);

  useEffect(() => {
    const fetchAssignment = async () => {
      if (!assignmentId) return;

      setIsLoading(true);
      setError(null);

      try {
        const { data: assignment, error } = await getAssignmentDetails(assignmentId);

        if (error) {
          throw error;
        }

        if (!assignment) {
          throw new Error('Assignment not found');
        }
        
        setAssignment(assignment);

        // Check if user is teacher for this class
        let teacherStatus = false;
        if (user && classId) {
          teacherStatus = await isTeacherForClass(classId);
          setIsTeacher(teacherStatus);

          // Fetch submissions for teachers
          if (teacherStatus) {
          const { data: submissionsData, error: submissionsError } = await getAssignmentSubmissions(assignmentId);

          if (submissionsError) {
            console.error('Error fetching submissions:', submissionsError);
          } else {
            setSubmissions(submissionsData || []);
          }
          }
        }

        // Fetch user's submission for students (if not teacher)
        if (!teacherStatus && user) {
          const { data: submissionData, error: submissionError } = await getSubmission(assignmentId, user.id);

          if (submissionData) {
            setUserSubmission(submissionData);

            // Fetch tickets for this submission
            const { data: ticketsData, error: ticketsError } = await getTicketsBySubmission(submissionData.id);
            if (ticketsData) {
              setUserTickets(ticketsData);
            }
          }
          // If no submission exists, that's fine - user can create one
        }
      } catch (err: any) {
        setError(err.message || 'Failed to load assignment details');
        console.error('Error fetching assignment:', err);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchAssignment();
  }, [assignmentId, classId, user, isTeacherForClass]);
  if (isLoading) {
    return <div className="h-64">
        <LoadingSpinner size="medium" />
      </div>;
  }
  
  if (error) {
    return <div className="max-w-4xl mx-auto">
        <div className="mb-6">
          <BackButton />
        </div>
        <ErrorAlert 
          message={error} 
          onDismiss={() => setError(null)} 
        />
      </div>;
  }
  return <div className="max-w-4xl mx-auto">
      <div className="mb-6">
        <BackButton to={`/classes/${classId}`} />
      </div>
      <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
        <div className="border-b border-gray-200 p-6">
          <h1 className="text-2xl font-bold mb-2">{assignment.title}</h1>
          <div className="flex items-center text-gray-500 mb-4">
            <CalendarIcon className="h-4 w-4 mr-1" />
            <span>Due {new Date(assignment.due_date).toLocaleDateString()}</span>
            <span className="mx-2">•</span>
            <span>{assignment.max_marks} points</span>
          </div>
          <p className="text-gray-700">{assignment.description}</p>
        </div>
        <div className="p-6 bg-gray-50 border-b border-gray-200">
          <h2 className="text-lg font-semibold mb-4">Assignment Content</h2>
          <div className="bg-white border border-gray-200 rounded-lg p-4 whitespace-pre-wrap">
            {assignment.content}
          </div>
        </div>
        {!isTeacher && (
          <div className="p-6">
            {userSubmission ? (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-medium text-green-800 mb-2">Assignment Submitted</h3>
                    <p className="text-sm text-green-700 mb-1">
                      File: {userSubmission.file_name}
                    </p>
                    <p className="text-sm text-green-700 mb-1">
                      Submitted: {new Date(userSubmission.submitted_at).toLocaleString()}
                    </p>
                    {userSubmission.grade !== null && (
                      <p className="text-sm text-green-700">
                        Grade: {userSubmission.grade}/{assignment.max_marks}
                      </p>
                    )}
                  </div>
                  <div className="flex space-x-2">
                    <Link
                      to={`/classes/${classId}/assignments/${assignmentId}/submit`}
                      className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm"
                    >
                      Update Submission
                    </Link>
                    {userSubmission.grade !== null && (
                      <button
                        onClick={() => setShowCreateTicket(true)}
                        className="bg-orange-600 text-white px-4 py-2 rounded-md hover:bg-orange-700 text-sm"
                      >
                        Dispute Grade
                      </button>
                    )}
                  </div>
                </div>

                {/* Show existing tickets */}
                {userTickets.length > 0 && (
                  <div className="mt-4 pt-4 border-t border-green-200">
                    <h4 className="font-medium text-green-800 mb-2">Grade Disputes:</h4>
                    <div className="space-y-2">
                      {userTickets.map((ticket) => (
                        <div key={ticket.id} className="bg-white border border-green-300 rounded p-3">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="font-medium text-sm">{ticket.title}</p>
                              <p className="text-xs text-gray-600">
                                Status: <span className={`font-medium ${
                                  ticket.status === 'resolved' ? 'text-green-600' :
                                  ticket.status === 'open' ? 'text-red-600' : 'text-yellow-600'
                                }`}>
                                  {ticket.status.toUpperCase()}
                                </span>
                              </p>
                            </div>
                            <span className="text-xs text-gray-500">
                              {new Date(ticket.created_at).toLocaleDateString()}
                            </span>
                          </div>
                          {ticket.response && (
                            <div className="mt-2 p-2 bg-blue-50 rounded text-sm">
                              <p className="font-medium text-blue-800">Teacher Response:</p>
                              <p className="text-blue-700">{ticket.response}</p>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="flex justify-center">
                <Link
                  to={`/classes/${classId}/assignments/${assignmentId}/submit`}
                  className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700"
                >
                  Submit Assignment
                </Link>
              </div>
            )}
          </div>
        )}
        {isTeacher && submissions.length > 0 && <div className="p-6">
            <h2 className="text-lg font-semibold mb-4">Student Submissions</h2>
            <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Student
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Marks
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Submitted
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {submissions.map((sub: any) => <tr key={sub.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="font-medium text-gray-900">
                          {sub.users?.name || 'Unknown Student'}
                        </div>
                        <div className="text-sm text-gray-500">
                          {sub.users?.email}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                          <CheckCircleIcon className="h-4 w-4 mr-1" />
                          Submitted
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {sub.grade !== null ? <span className="text-gray-900">
                            {sub.grade}/{assignment.max_marks}
                          </span> : <span className="text-gray-500">Pending</span>}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(sub.submitted_at).toLocaleString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <Link
                          to={`/assignments/${assignmentId}/submissions/${sub.id}`}
                          className="text-blue-600 hover:text-blue-800 font-medium"
                        >
                          Review
                        </Link>
                      </td>
                    </tr>)}
                </tbody>
              </table>
              </div>
            </div>
          </div>}
      </div>

      {/* Create Ticket Modal */}
      {showCreateTicket && userSubmission && (
        <CreateTicket
          submissionId={userSubmission.id}
          assignmentTitle={assignment.title}
          currentGrade={userSubmission.grade}
          maxMarks={assignment.max_marks}
          onTicketCreated={() => {
            setShowCreateTicket(false);
            // Refresh tickets
            if (userSubmission) {
              getTicketsBySubmission(userSubmission.id).then(({ data }) => {
                if (data) setUserTickets(data);
              });
            }
          }}
          onCancel={() => setShowCreateTicket(false)}
        />
      )}
    </div>;
};
export default AssignmentDetail;