import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import Layout from './components/layout/Layout';
import Dashboard from './pages/Dashboard';
import Classes from './pages/Classes';
import ClassDetail from './pages/ClassDetail';
import Calendar from './pages/Calendar';
import People from './pages/People';
import AssignmentDetail from './pages/AssignmentDetail';
import CreateAssignment from './pages/CreateAssignment';
import SubmitAssignment from './pages/SubmitAssignment';
import ReviewSubmission from './pages/ReviewSubmission';
import Login from './pages/Login';
import Profile from './pages/Profile';
import CreateClass from './pages/CreateClass';
import JoinClass from './pages/JoinClass';
import ClassStudents from './pages/ClassStudents';
import Tickets from './pages/Tickets';
import { AuthProvider } from './context/AuthContext';
import { NotificationProvider } from './context/NotificationContext';
import { ErrorProvider } from './context/ErrorContext';
import ProtectedRoute from './components/ProtectedRoute';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
export function App() {
  return <AuthProvider>
      <ErrorProvider>
        <NotificationProvider>
          <Router>
            <ToastContainer position="top-right" autoClose={3000} />
            <Routes>
          <Route path="/login" element={<Login />} />
          <Route path="/" element={<Layout />}>
            <Route index element={<Dashboard />} />
            <Route path="classes" element={<Classes />} />
            <Route path="classes/create" element={<CreateClass />} />
            <Route path="classes/join" element={<JoinClass />} />
            <Route path="classes/:classId" element={
              <ProtectedRoute requireAuth={true} requireClassMember={true} requireClassExists={true}>
                <ClassDetail />
              </ProtectedRoute>
            } />
            <Route path="classes/:classId/students" element={
              <ProtectedRoute requireAuth={true} requireTeacher={true} requireClassExists={true}>
                <ClassStudents />
              </ProtectedRoute>
            } />
            <Route path="classes/:classId/assignments/:assignmentId" element={
              <ProtectedRoute requireAuth={true} requireClassMember={true} requireClassExists={true}>
                <AssignmentDetail />
              </ProtectedRoute>
            } />
            <Route path="classes/:classId/create-assignment" element={
              <ProtectedRoute requireAuth={true} requireTeacher={true} requireClassExists={true}>
                <CreateAssignment />
              </ProtectedRoute>
            } />
            <Route path="classes/:classId/assignments/:assignmentId/submit" element={
              <ProtectedRoute requireAuth={true} requireClassMember={true} requireClassExists={true}>
                <SubmitAssignment />
              </ProtectedRoute>
            } />
            <Route path="classes/:classId/assignments/:assignmentId/submissions/:submissionId" element={
              <ProtectedRoute requireAuth={true} requireTeacher={true} requireClassExists={true}>
                <ReviewSubmission />
              </ProtectedRoute>
            } />
            <Route path="calendar" element={<Calendar />} />
            <Route path="people" element={<People />} />
            <Route path="tickets" element={<Tickets />} />
            <Route path="profile" element={<Profile />} />
          </Route>
          <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
        </Router>
      </NotificationProvider>
    </ErrorProvider>
  </AuthProvider>;
}